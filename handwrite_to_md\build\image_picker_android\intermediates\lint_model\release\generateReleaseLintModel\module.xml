<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android"
    name=":image_picker_android"
    type="LIBRARY"
    maven="io.flutter.plugins.imagepicker:image_picker_android:1.0-SNAPSHOT"
    agpVersion="8.7.0"
    buildFolder="D:\android\handwritetomd\handwrite_to_md\build\image_picker_android"
    bootClassPath="D:\Tools\android_sdk\platforms\android-35\android.jar;D:\Tools\android_sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      disable="AndroidGradlePluginVersion,InvalidPackage,GradleDependency,NewerVersionAvailable"
      abortOnError="true"
      absolutePaths="true"
      checkAllWarnings="true"
      warningsAsErrors="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="AndroidGradlePluginVersion"
        severity="IGNORE" />
      <severity
        id="GradleDependency"
        severity="IGNORE" />
      <severity
        id="InvalidPackage"
        severity="IGNORE" />
      <severity
        id="NewerVersionAvailable"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
