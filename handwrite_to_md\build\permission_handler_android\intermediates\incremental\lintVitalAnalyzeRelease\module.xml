<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\permission_handler_android-12.1.0\android"
    name=":permission_handler_android"
    type="LIBRARY"
    maven="com.baseflow.permissionhandler:permission_handler_android:1.0"
    agpVersion="8.7.0"
    buildFolder="D:\android\handwritetomd\handwrite_to_md\build\permission_handler_android"
    bootClassPath="D:\Tools\android_sdk\platforms\android-34\android.jar;D:\Tools\android_sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
