<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_secure_storage-9.2.4\android"
    name=":flutter_secure_storage"
    type="LIBRARY"
    maven="com.it_nomads.fluttersecurestorage:flutter_secure_storage:1.0-SNAPSHOT"
    agpVersion="8.7.0"
    buildFolder="D:\android\handwritetomd\handwrite_to_md\build\flutter_secure_storage"
    bootClassPath="D:\Tools\android_sdk\platforms\android-34\android.jar;D:\Tools\android_sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
