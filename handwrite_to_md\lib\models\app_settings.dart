class AppSettings {
  final String? markdownFolder;
  final String? watchFolder;
  final String? llmBaseUrl;
  final String? llmApiKey;
  final String? llmModel;
  final String? systemPrompt;
  final String? backupApiUrl;
  final String? backupApiKey;
  final String? webdavUrl;
  final String? webdavUsername;
  final String? webdavPassword;
  final String? webdavRemotePath;

  AppSettings({
    this.markdownFolder,
    this.watchFolder,
    this.llmBaseUrl,
    this.llmApiKey,
    this.llmModel,
    this.systemPrompt,
    this.backupApiUrl,
    this.backupApiKey,
    this.webdavUrl,
    this.webdavUsername,
    this.webdavPassword,
    this.webdavRemotePath,
  });

  AppSettings copyWith({
    String? markdownFolder,
    String? watchFolder,
    String? llmBaseUrl,
    String? llmApiKey,
    String? llmModel,
    String? systemPrompt,
    String? backupApiUrl,
    String? backupApiKey,
    String? webdavUrl,
    String? webdavUsername,
    String? webdavPassword,
    String? webdavRemotePath,
  }) {
    return AppSettings(
      markdownFolder: markdownFolder ?? this.markdownFolder,
      watchFolder: watchFolder ?? this.watchFolder,
      llmBaseUrl: llmBaseUrl ?? this.llmBaseUrl,
      llmApiKey: llmApiKey ?? this.llmApiKey,
      llmModel: llmModel ?? this.llmModel,
      systemPrompt: systemPrompt ?? this.systemPrompt,
      backupApiUrl: backupApiUrl ?? this.backupApiUrl,
      backupApiKey: backupApiKey ?? this.backupApiKey,
      webdavUrl: webdavUrl ?? this.webdavUrl,
      webdavUsername: webdavUsername ?? this.webdavUsername,
      webdavPassword: webdavPassword ?? this.webdavPassword,
      webdavRemotePath: webdavRemotePath ?? this.webdavRemotePath,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'markdownFolder': markdownFolder,
      'watchFolder': watchFolder,
      'llmBaseUrl': llmBaseUrl,
      'llmApiKey': llmApiKey,
      'llmModel': llmModel,
      'systemPrompt': systemPrompt,
      'backupApiUrl': backupApiUrl,
      'backupApiKey': backupApiKey,
      'webdavUrl': webdavUrl,
      'webdavUsername': webdavUsername,
      'webdavPassword': webdavPassword,
      'webdavRemotePath': webdavRemotePath,
    };
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      markdownFolder: json['markdownFolder'],
      watchFolder: json['watchFolder'],
      llmBaseUrl: json['llmBaseUrl'],
      llmApiKey: json['llmApiKey'],
      llmModel: json['llmModel'],
      systemPrompt: json['systemPrompt'],
      backupApiUrl: json['backupApiUrl'],
      backupApiKey: json['backupApiKey'],
      webdavUrl: json['webdavUrl'],
      webdavUsername: json['webdavUsername'],
      webdavPassword: json['webdavPassword'],
      webdavRemotePath: json['webdavRemotePath'],
    );
  }

  bool get isLLMConfigured {
    return llmBaseUrl != null &&
        llmBaseUrl!.isNotEmpty &&
        llmApiKey != null &&
        llmApiKey!.isNotEmpty &&
        llmModel != null &&
        llmModel!.isNotEmpty;
  }

  bool get isBackupConfigured {
    return backupApiUrl != null &&
        backupApiUrl!.isNotEmpty &&
        backupApiKey != null &&
        backupApiKey!.isNotEmpty;
  }

  bool get isWebDAVConfigured {
    return webdavUrl != null &&
        webdavUrl!.isNotEmpty &&
        webdavUsername != null &&
        webdavUsername!.isNotEmpty &&
        webdavPassword != null &&
        webdavPassword!.isNotEmpty;
  }
}
