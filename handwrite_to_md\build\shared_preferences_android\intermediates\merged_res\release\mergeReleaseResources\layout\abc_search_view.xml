<?xml version="1.0" encoding="utf-8"?>
<!--
/*
 * Copyright (C) 2014 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/search_bar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

    <!-- This is actually used for the badge icon *or* the badge label (or neither) -->
    <TextView
            android:id="@+id/search_badge"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:layout_marginBottom="2dip"
            android:drawablePadding="0dip"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:textColor="?android:attr/textColorPrimary"
            android:visibility="gone" />

    <ImageView
            android:id="@+id/search_button"
            style="?attr/actionButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:focusable="true"
            android:contentDescription="@string/abc_searchview_description_search" />

    <LinearLayout
            android:id="@+id/search_edit_frame"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:layout_marginLeft="8dip"
            android:layout_marginRight="8dip"
            android:orientation="horizontal"
            android:layoutDirection="locale">

        <ImageView
                android:id="@+id/search_mag_icon"
                android:layout_width="@dimen/abc_dropdownitem_icon_width"
                android:layout_height="wrap_content"
                android:scaleType="centerInside"
                android:layout_gravity="center_vertical"
                android:visibility="gone"
                style="@style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon" />

        <!-- Inner layout contains the app icon, button(s) and EditText -->
        <LinearLayout
                android:id="@+id/search_plate"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_gravity="center_vertical"
                android:orientation="horizontal">

            <view class="androidx.appcompat.widget.SearchView$SearchAutoComplete"
                  android:id="@+id/search_src_text"
                  android:layout_height="36dip"
                  android:layout_width="0dp"
                  android:layout_weight="1"
                  android:layout_gravity="center_vertical"
                  android:paddingLeft="@dimen/abc_dropdownitem_text_padding_left"
                  android:paddingRight="@dimen/abc_dropdownitem_text_padding_right"
                  android:singleLine="true"
                  android:ellipsize="end"
                  android:background="@null"
                  android:inputType="text|textAutoComplete|textNoSuggestions"
                  android:imeOptions="actionSearch"
                  android:dropDownHeight="wrap_content"
                  android:dropDownAnchor="@id/search_edit_frame"
                  android:dropDownVerticalOffset="0dip"
                  android:dropDownHorizontalOffset="0dip" />

            <ImageView
                    android:id="@+id/search_close_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingLeft="8dip"
                    android:paddingRight="8dip"
                    android:layout_gravity="center_vertical"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:focusable="true"
                    android:contentDescription="@string/abc_searchview_description_clear" />

        </LinearLayout>

        <LinearLayout
                android:id="@+id/submit_area"
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="match_parent">

            <ImageView
                    android:id="@+id/search_go_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="16dip"
                    android:paddingRight="16dip"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:visibility="gone"
                    android:focusable="true"
                    android:contentDescription="@string/abc_searchview_description_submit" />

            <ImageView
                    android:id="@+id/search_voice_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="16dip"
                    android:paddingRight="16dip"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:visibility="gone"
                    android:focusable="true"
                    android:contentDescription="@string/abc_searchview_description_voice" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
