import 'dart:io';
import 'package:flutter/material.dart';
import '../models/app_settings.dart';
import '../services/webdav_service.dart';
import '../services/temp_file_manager.dart';
import 'batch_process_page.dart';

class WebDAVBatchProcessPage extends StatefulWidget {
  final AppSettings settings;

  const WebDAVBatchProcessPage({
    super.key,
    required this.settings,
  });

  @override
  State<WebDAVBatchProcessPage> createState() => _WebDAVBatchProcessPageState();
}

class _WebDAVBatchProcessPageState extends State<WebDAVBatchProcessPage> {
  final WebDAVService _webdavService = WebDAVService();
  List<WebDAVFile> _remoteFiles = [];
  List<WebDAVFile> _selectedFiles = [];
  bool _isLoading = false;
  bool _isConnecting = false;
  bool _isDownloading = false;
  String? _errorMessage;
  int _downloadProgress = 0;
  int _totalDownloads = 0;
  String _currentDownloadFile = '';

  @override
  void initState() {
    super.initState();
    _connectAndLoadFiles();
  }

  @override
  void dispose() {
    _webdavService.dispose();
    super.dispose();
  }

  Future<void> _connectAndLoadFiles() async {
    setState(() {
      _isConnecting = true;
      _errorMessage = null;
    });

    try {
      // 连接WebDAV
      final connected = await _webdavService.connect(widget.settings);
      if (!connected) {
        throw Exception('无法连接到WebDAV服务器');
      }

      // 获取远程路径
      final remotePath = widget.settings.webdavRemotePath ?? '/';
      
      // 加载图片文件
      final files = await _webdavService.listImageFiles(remotePath);
      
      setState(() {
        _remoteFiles = files;
        _selectedFiles = List.from(files); // 默认选择所有文件
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isConnecting = false;
      });
    }
  }

  Future<void> _downloadAndProcess() async {
    if (_selectedFiles.isEmpty) {
      _showErrorSnackBar('请选择要处理的文件');
      return;
    }

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0;
      _totalDownloads = _selectedFiles.length;
    });

    try {
      // 创建会话临时目录
      final sessionDir = await TempFileManager.createSessionDirectory();
      
      // 下载选中的文件
      final downloadedFiles = await _webdavService.downloadFiles(
        _selectedFiles,
        sessionDir.path,
        (current, total, fileName) {
          setState(() {
            _downloadProgress = current;
            _currentDownloadFile = fileName;
          });
        },
      );

      if (downloadedFiles.isEmpty) {
        throw Exception('没有成功下载任何文件');
      }

      // 跳转到批量处理页面
      if (mounted) {
        final result = await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => BatchProcessPage(
              imageFiles: downloadedFiles,
              settings: widget.settings,
              isWebDAVMode: true,
              webdavFiles: _selectedFiles,
              sessionDirectory: sessionDir,
            ),
          ),
        );

        // 如果用户完成了处理，返回主页面
        if (result == true && mounted) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      _showErrorDialog('下载失败', e.toString());
    } finally {
      setState(() {
        _isDownloading = false;
      });
    }
  }

  void _toggleFileSelection(WebDAVFile file) {
    setState(() {
      if (_selectedFiles.contains(file)) {
        _selectedFiles.remove(file);
      } else {
        _selectedFiles.add(file);
      }
    });
  }

  void _selectAll() {
    setState(() {
      _selectedFiles = List.from(_remoteFiles);
    });
  }

  void _deselectAll() {
    setState(() {
      _selectedFiles.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isDownloading 
            ? '下载中 $_downloadProgress/$_totalDownloads'
            : 'WebDAV批量处理'),
        centerTitle: true,
        actions: [
          if (!_isConnecting && !_isDownloading && _remoteFiles.isNotEmpty) ...[
            IconButton(
              icon: const Icon(Icons.select_all),
              onPressed: _selectAll,
              tooltip: '全选',
            ),
            IconButton(
              icon: const Icon(Icons.deselect),
              onPressed: _deselectAll,
              tooltip: '取消全选',
            ),
          ],
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildBody() {
    if (_isConnecting) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在连接WebDAV服务器...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              '连接失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage!,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _connectAndLoadFiles,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_isDownloading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text('正在下载文件 $_downloadProgress/$_totalDownloads'),
            const SizedBox(height: 8),
            Text(
              _currentDownloadFile,
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: LinearProgressIndicator(
                value: _totalDownloads > 0 ? _downloadProgress / _totalDownloads : 0,
              ),
            ),
          ],
        ),
      );
    }

    if (_remoteFiles.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_not_supported_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text('远程目录中没有找到图片文件'),
          ],
        ),
      );
    }

    return Column(
      children: [
        // 信息提示
        Container(
          width: double.infinity,
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue.shade700),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '找到 ${_remoteFiles.length} 个图片文件',
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '已选择 ${_selectedFiles.length} 个文件进行处理',
                      style: TextStyle(
                        color: Colors.blue.shade600,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '文件将先下载到本地临时目录，处理完成后可选择删除远程文件',
                      style: TextStyle(
                        color: Colors.blue.shade500,
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        // 文件列表
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _remoteFiles.length,
            itemBuilder: (context, index) => _buildFileItem(_remoteFiles[index]),
          ),
        ),
      ],
    );
  }

  Widget _buildFileItem(WebDAVFile file) {
    final isSelected = _selectedFiles.contains(file);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Checkbox(
          value: isSelected,
          onChanged: (_) => _toggleFileSelection(file),
        ),
        title: Text(
          file.name,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        subtitle: Text(
          '${(file.size / 1024).toStringAsFixed(1)} KB'
          '${file.lastModified != null ? ' • ${_formatDate(file.lastModified!)}' : ''}',
        ),
        trailing: const Icon(Icons.image),
        onTap: () => _toggleFileSelection(file),
      ),
    );
  }

  Widget _buildBottomBar() {
    if (_isConnecting || _isDownloading || _remoteFiles.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: ElevatedButton(
          onPressed: _selectedFiles.isNotEmpty ? _downloadAndProcess : null,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: Text(
            '下载并处理 (${_selectedFiles.length})',
            style: const TextStyle(fontSize: 16),
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
