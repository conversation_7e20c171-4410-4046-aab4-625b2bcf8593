1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.handwrite_to_md"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- 相机权限 -->
11    <uses-permission android:name="android.permission.CAMERA" />
11-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:4:5-65
11-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:4:22-62
12
13    <!-- 存储权限 (兼容所有Android版本) -->
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:7:5-80
14-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:7:22-77
15    <uses-permission
15-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:8:5-10:40
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:8:22-78
17        android:maxSdkVersion="28" />
17-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:9:9-35
18
19    <!-- Android 13+ 媒体权限 -->
20    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
20-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:13:5-76
20-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:13:22-73
21    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
21-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:14:5-75
21-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:14:22-72
22    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
22-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:15:5-75
22-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:15:22-72
23
24    <!-- Android 11+ 管理外部存储权限 (最全面的文件访问权限) -->
25    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
25-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:18:5-82
25-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:18:22-79
26
27    <!-- 网络权限 -->
28    <uses-permission android:name="android.permission.INTERNET" />
28-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:21:5-67
28-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:21:22-64
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:22:5-79
29-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:22:22-76
30
31    <!-- 请求忽略电池优化，避免后台权限问题 -->
32    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
32-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:25:5-95
32-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:25:22-92
33    <!--
34         Required to query activities that can process text, see:
35         https://developer.android.com/training/package-visibility and
36         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
37
38         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
39    -->
40    <queries>
40-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:64:5-69:15
41        <intent>
41-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:65:9-68:18
42            <action android:name="android.intent.action.PROCESS_TEXT" />
42-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:66:13-72
42-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:66:21-70
43
44            <data android:mimeType="text/plain" />
44-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:67:13-50
44-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:67:19-48
45        </intent>
46        <intent>
46-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
47            <action android:name="android.intent.action.GET_CONTENT" />
47-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
47-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:21-69
48
49            <data android:mimeType="*/*" />
49-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:67:13-50
49-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:67:19-48
50        </intent>
51    </queries>
52
53    <permission
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
54        android:name="com.example.handwrite_to_md.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
55        android:protectionLevel="signature" />
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
56
57    <uses-permission android:name="com.example.handwrite_to_md.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
58
59    <application
60        android:name="android.app.Application"
60-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:29:9-42
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
62        android:extractNativeLibs="true"
63        android:icon="@mipmap/ic_launcher"
63-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:30:9-43
64        android:label="ToMDs" >
64-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:28:9-30
65        <activity
65-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:31:9-52:20
66            android:name="com.example.handwrite_to_md.MainActivity"
66-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:32:13-41
67            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
67-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:37:13-163
68            android:exported="true"
68-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:33:13-36
69            android:hardwareAccelerated="true"
69-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:38:13-47
70            android:launchMode="singleTop"
70-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:34:13-43
71            android:taskAffinity=""
71-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:35:13-36
72            android:theme="@style/LaunchTheme"
72-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:36:13-47
73            android:windowSoftInputMode="adjustResize" >
73-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:39:13-55
74
75            <!--
76                 Specifies an Android theme to apply to this Activity as soon as
77                 the Android process has started. This theme is visible to the user
78                 while the Flutter UI initializes. After that, this theme continues
79                 to determine the Window background behind the Flutter UI.
80            -->
81            <meta-data
81-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:44:13-47:17
82                android:name="io.flutter.embedding.android.NormalTheme"
82-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:45:15-70
83                android:resource="@style/NormalTheme" />
83-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:46:15-52
84
85            <intent-filter>
85-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:48:13-51:29
86                <action android:name="android.intent.action.MAIN" />
86-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:49:17-68
86-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:49:25-66
87
88                <category android:name="android.intent.category.LAUNCHER" />
88-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:50:17-76
88-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:50:27-74
89            </intent-filter>
90        </activity>
91        <!--
92             Don't delete the meta-data below.
93             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
94        -->
95        <meta-data
95-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:55:9-57:33
96            android:name="flutterEmbedding"
96-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:56:13-44
97            android:value="2" />
97-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:57:13-30
98
99        <provider
99-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
100            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
100-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
101            android:authorities="com.example.handwrite_to_md.flutter.image_provider"
101-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
102            android:exported="false"
102-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
103            android:grantUriPermissions="true" >
103-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
104            <meta-data
104-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
105                android:name="android.support.FILE_PROVIDER_PATHS"
105-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
106                android:resource="@xml/flutter_image_picker_file_paths" />
106-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
107        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
108        <service
108-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
109            android:name="com.google.android.gms.metadata.ModuleDependencies"
109-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
110            android:enabled="false"
110-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
111            android:exported="false" >
111-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
112            <intent-filter>
112-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
113                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
113-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
113-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
114            </intent-filter>
115
116            <meta-data
116-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
117                android:name="photopicker_activity:0:required"
117-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
118                android:value="" />
118-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
119        </service>
120
121        <uses-library
121-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
122            android:name="androidx.window.extensions"
122-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
123            android:required="false" />
123-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
124        <uses-library
124-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
125            android:name="androidx.window.sidecar"
125-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
126            android:required="false" />
126-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
127
128        <provider
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
129            android:name="androidx.startup.InitializationProvider"
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
130            android:authorities="com.example.handwrite_to_md.androidx-startup"
130-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
131            android:exported="false" >
131-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
132            <meta-data
132-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
133-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
134                android:value="androidx.startup" />
134-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
135            <meta-data
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
136                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
137                android:value="androidx.startup" />
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
138        </provider>
139
140        <receiver
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
141            android:name="androidx.profileinstaller.ProfileInstallReceiver"
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
142            android:directBootAware="false"
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
143            android:enabled="true"
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
144            android:exported="true"
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
145            android:permission="android.permission.DUMP" >
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
146            <intent-filter>
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
147                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
148            </intent-filter>
149            <intent-filter>
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
150                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
151            </intent-filter>
152            <intent-filter>
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
153                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
154            </intent-filter>
155            <intent-filter>
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
156                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
157            </intent-filter>
158        </receiver>
159    </application>
160
161</manifest>
