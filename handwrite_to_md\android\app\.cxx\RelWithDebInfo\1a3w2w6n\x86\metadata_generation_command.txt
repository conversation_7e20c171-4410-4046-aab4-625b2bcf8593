                        -HD:\Tools\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=D:\Tools\android_sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\Tools\android_sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=D:\Tools\android_sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\Tools\android_sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\android\handwritetomd\handwrite_to_md\build\app\intermediates\cxx\RelWithDebInfo\1a3w2w6n\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\android\handwritetomd\handwrite_to_md\build\app\intermediates\cxx\RelWithDebInfo\1a3w2w6n\obj\x86
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\android\handwritetomd\handwrite_to_md\android\app\.cxx\RelWithDebInfo\1a3w2w6n\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2