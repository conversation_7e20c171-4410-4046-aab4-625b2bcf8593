import 'dart:io';
import 'dart:typed_data';
import 'package:webdav_client/webdav_client.dart';
import '../models/app_settings.dart';

class WebDAVFile {
  final String name;
  final String path;
  final int size;
  final DateTime? lastModified;
  final bool isDirectory;

  WebDAVFile({
    required this.name,
    required this.path,
    required this.size,
    this.lastModified,
    required this.isDirectory,
  });

  bool get isImage {
    if (isDirectory) return false;
    final ext = name.toLowerCase();
    return ext.endsWith('.jpg') ||
        ext.endsWith('.jpeg') ||
        ext.endsWith('.png') ||
        ext.endsWith('.bmp') ||
        ext.endsWith('.gif') ||
        ext.endsWith('.webp');
  }
}

class WebDAVService {
  late Client _client;
  bool _isConnected = false;

  bool get isConnected => _isConnected;

  /// 初始化WebDAV连接
  Future<bool> connect(AppSettings settings) async {
    if (!settings.isWebDAVConfigured) {
      return false;
    }

    try {
      _client = newClient(
        settings.webdavUrl!,
        user: settings.webdavUsername!,
        password: settings.webdavPassword!,
        debug: false,
      );

      // 测试连接
      await _client.ping();
      _isConnected = true;
      return true;
    } catch (e) {
      _isConnected = false;
      return false;
    }
  }

  /// 测试WebDAV连接
  Future<bool> testConnection(AppSettings settings) async {
    if (!settings.isWebDAVConfigured) {
      return false;
    }

    try {
      final client = newClient(
        settings.webdavUrl!,
        user: settings.webdavUsername!,
        password: settings.webdavPassword!,
        debug: false,
      );

      await client.ping();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取指定目录下的文件列表
  Future<List<WebDAVFile>> listFiles(String remotePath) async {
    if (!_isConnected) {
      throw Exception('WebDAV未连接');
    }

    try {
      final files = await _client.readDir(remotePath);
      return files.map((file) => WebDAVFile(
        name: file.name ?? '',
        path: file.path ?? '',
        size: file.size ?? 0,
        lastModified: file.mTime,
        isDirectory: file.isDir ?? false,
      )).toList();
    } catch (e) {
      throw Exception('获取文件列表失败: $e');
    }
  }

  /// 获取指定目录下的所有图片文件
  Future<List<WebDAVFile>> listImageFiles(String remotePath) async {
    final allFiles = await listFiles(remotePath);
    return allFiles.where((file) => file.isImage).toList();
  }

  /// 下载文件到本地
  Future<File> downloadFile(WebDAVFile webdavFile, String localPath) async {
    if (!_isConnected) {
      throw Exception('WebDAV未连接');
    }

    try {
      final bytes = await _client.read(webdavFile.path);
      final localFile = File(localPath);
      
      // 确保目录存在
      await localFile.parent.create(recursive: true);
      
      // 写入文件
      await localFile.writeAsBytes(bytes);
      return localFile;
    } catch (e) {
      throw Exception('下载文件失败: $e');
    }
  }

  /// 批量下载文件
  Future<List<File>> downloadFiles(
    List<WebDAVFile> webdavFiles,
    String localDirectory,
    Function(int current, int total, String fileName)? onProgress,
  ) async {
    final downloadedFiles = <File>[];

    for (int i = 0; i < webdavFiles.length; i++) {
      final webdavFile = webdavFiles[i];
      onProgress?.call(i + 1, webdavFiles.length, webdavFile.name);

      try {
        final localPath = '$localDirectory/${webdavFile.name}';
        final localFile = await downloadFile(webdavFile, localPath);
        downloadedFiles.add(localFile);
      } catch (e) {
        // 记录错误但继续下载其他文件
        print('下载文件 ${webdavFile.name} 失败: $e');
      }
    }

    return downloadedFiles;
  }

  /// 删除远程文件
  Future<bool> deleteFile(String remotePath) async {
    if (!_isConnected) {
      throw Exception('WebDAV未连接');
    }

    try {
      await _client.remove(remotePath);
      return true;
    } catch (e) {
      print('删除远程文件失败: $e');
      return false;
    }
  }

  /// 批量删除远程文件
  Future<List<bool>> deleteFiles(List<String> remotePaths) async {
    final results = <bool>[];

    for (final remotePath in remotePaths) {
      final success = await deleteFile(remotePath);
      results.add(success);
    }

    return results;
  }

  /// 断开连接
  void disconnect() {
    _isConnected = false;
  }

  /// 释放资源
  void dispose() {
    disconnect();
  }
}
