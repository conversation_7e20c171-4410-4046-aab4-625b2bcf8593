import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/app_settings.dart';

class SettingsRepository {
  static const String _keyMarkdownFolder = 'markdown_folder';
  static const String _keyWatchFolder = 'watch_folder';
  static const String _keyLLMBaseUrl = 'llm_base_url';
  static const String _keyLLMModel = 'llm_model';
  static const String _keySystemPrompt = 'system_prompt';
  static const String _keyBackupApiUrl = 'backup_api_url';
  static const String _keyWebDAVUrl = 'webdav_url';
  static const String _keyWebDAVUsername = 'webdav_username';
  static const String _keyWebDAVRemotePath = 'webdav_remote_path';

  // 敏感信息使用加密存储
  static const String _keyLLMApiKey = 'llm_api_key';
  static const String _keyBackupApiKey = 'backup_api_key';
  static const String _keyWebDAVPassword = 'webdav_password';

  final SharedPreferences _prefs;
  final FlutterSecureStorage _secureStorage;

  SettingsRepository(this._prefs, this._secureStorage);

  static Future<SettingsRepository> create() async {
    final prefs = await SharedPreferences.getInstance();
    const secureStorage = FlutterSecureStorage(
      aOptions: AndroidOptions(
        encryptedSharedPreferences: true,
      ),
      iOptions: IOSOptions(
        accessibility: KeychainAccessibility.first_unlock_this_device,
      ),
    );
    return SettingsRepository(prefs, secureStorage);
  }

  // 保存普通设置
  Future<void> saveMarkdownFolder(String path) async {
    await _prefs.setString(_keyMarkdownFolder, path);
  }

  Future<void> saveWatchFolder(String path) async {
    await _prefs.setString(_keyWatchFolder, path);
  }

  Future<void> saveFolderSettings({
    required String markdownFolder,
    required String watchFolder,
  }) async {
    await _prefs.setString(_keyMarkdownFolder, markdownFolder);
    await _prefs.setString(_keyWatchFolder, watchFolder);
  }

  Future<void> saveLLMSettings({
    required String baseUrl,
    required String apiKey,
    required String model,
    required String systemPrompt,
  }) async {
    await _prefs.setString(_keyLLMBaseUrl, baseUrl);
    await _prefs.setString(_keyLLMModel, model);
    await _prefs.setString(_keySystemPrompt, systemPrompt);
    await _secureStorage.write(key: _keyLLMApiKey, value: apiKey);
  }

  Future<void> saveBackupSettings({
    required String apiUrl,
    required String apiKey,
  }) async {
    await _prefs.setString(_keyBackupApiUrl, apiUrl);
    await _secureStorage.write(key: _keyBackupApiKey, value: apiKey);
  }

  Future<void> saveWebDAVSettings({
    required String url,
    required String username,
    required String password,
    String? remotePath,
  }) async {
    await _prefs.setString(_keyWebDAVUrl, url);
    await _prefs.setString(_keyWebDAVUsername, username);
    await _prefs.setString(_keyWebDAVRemotePath, remotePath ?? '/');
    await _secureStorage.write(key: _keyWebDAVPassword, value: password);
  }

  // 读取设置
  Future<AppSettings> loadSettings() async {
    return AppSettings(
      markdownFolder: _prefs.getString(_keyMarkdownFolder),
      watchFolder: _prefs.getString(_keyWatchFolder),
      llmBaseUrl: _prefs.getString(_keyLLMBaseUrl),
      llmApiKey: await _secureStorage.read(key: _keyLLMApiKey),
      llmModel: _prefs.getString(_keyLLMModel),
      systemPrompt: _prefs.getString(_keySystemPrompt) ?? _getDefaultSystemPrompt(),
      backupApiUrl: _prefs.getString(_keyBackupApiUrl),
      backupApiKey: await _secureStorage.read(key: _keyBackupApiKey),
      webdavUrl: _prefs.getString(_keyWebDAVUrl),
      webdavUsername: _prefs.getString(_keyWebDAVUsername),
      webdavPassword: await _secureStorage.read(key: _keyWebDAVPassword),
      webdavRemotePath: _prefs.getString(_keyWebDAVRemotePath),
    );
  }

  // 清除所有设置
  Future<void> clearAllSettings() async {
    await _prefs.clear();
    await _secureStorage.deleteAll();
  }

  // 清除敏感信息
  Future<void> clearSecureData() async {
    await _secureStorage.delete(key: _keyLLMApiKey);
    await _secureStorage.delete(key: _keyBackupApiKey);
    await _secureStorage.delete(key: _keyWebDAVPassword);
  }

  String _getDefaultSystemPrompt() {
    return """你是一个专业的手写文字识别助手。请分析图片中的手写内容：

1. 准确识别所有手写文字内容，可能为中英文混合。
2. 保持原文的段落结构和逻辑顺序
3. 输出格式必须是标准Markdown格式
4. 如果有标题，请用适当的#标记层级
5. 如果有列表，请用-或数字标记
6. 忽略涂改、装饰性内容和无关标记
7. 如果内容不清晰，请用[不清晰]标注
8. 如果有表格，请用|和-来表示
9. 如果内容中有用五角星开头的内容，则将该内容作为markdown文件的标题，如果没有，你来根据内容自动生成一个标题

请直接输出转换结果，不要添加其他说明文字。""";
  }

  // 验证设置
  Future<bool> validateLLMSettings() async {
    final settings = await loadSettings();
    return settings.isLLMConfigured;
  }

  Future<bool> validateBackupSettings() async {
    final settings = await loadSettings();
    return settings.isBackupConfigured;
  }

  Future<bool> validateWebDAVSettings() async {
    final settings = await loadSettings();
    return settings.isWebDAVConfigured;
  }

  // 获取默认Markdown保存文件夹路径
  String getDefaultMarkdownFolder() {
    return '/storage/emulated/0/HandwriteApp/markdown/';
  }

  // 获取默认监控文件夹路径
  String getDefaultWatchFolder() {
    return '/storage/emulated/0/HandwriteApp/watch/';
  }


}
