<variant
    name="release"
    package="com.mr.flutter.plugin.filepicker"
    minSdkVersion="21"
    targetSdkVersion="21"
    mergedManifest="D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="D:\android\handwritetomd\handwrite_to_md\build\file_picker\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\default_proguard_files\global\proguard-android.txt-8.7.0"
    partialResultsDir="D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\lint_partial_results\release\lintAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="com.mr.flutter.plugin.filepicker"
      generatedSourceFolders="D:\android\handwritetomd\handwrite_to_md\build\file_picker\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\android\handwritetomd\handwrite_to_md\build\file_picker\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58b2fa97deb6620e81e8f2146e68c814\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
