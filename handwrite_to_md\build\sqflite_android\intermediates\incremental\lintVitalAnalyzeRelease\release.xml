<variant
    name="release"
    package="com.tekartik.sqflite"
    minSdkVersion="19"
    targetSdkVersion="19"
    mergedManifest="D:\android\handwritetomd\handwrite_to_md\build\sqflite_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    proguardFiles="D:\android\handwritetomd\handwrite_to_md\build\sqflite_android\intermediates\default_proguard_files\global\proguard-android.txt-8.7.0"
    partialResultsDir="D:\android\handwritetomd\handwrite_to_md\build\sqflite_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\845d9227b0aa7afd95c9677206a478c7\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="D:\android\handwritetomd\handwrite_to_md\build\sqflite_android\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\android\handwritetomd\handwrite_to_md\build\sqflite_android\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="com.tekartik.sqflite"
      generatedSourceFolders="D:\android\handwritetomd\handwrite_to_md\build\sqflite_android\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\android\handwritetomd\handwrite_to_md\build\sqflite_android\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\845d9227b0aa7afd95c9677206a478c7\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
