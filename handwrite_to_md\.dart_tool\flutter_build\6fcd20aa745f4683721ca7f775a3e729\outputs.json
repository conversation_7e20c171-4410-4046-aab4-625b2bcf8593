["D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\shaders/ink_sparkle.frag", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.json", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.bin", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\FontManifest.json", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NOTICES.Z", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NativeAssetsManifest.json", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\release\\x86_64\\app.so", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\release\\armeabi-v7a\\app.so", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\release\\arm64-v8a\\app.so"]