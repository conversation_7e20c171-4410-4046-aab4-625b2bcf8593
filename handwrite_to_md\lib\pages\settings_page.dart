import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../models/app_settings.dart';
import '../services/settings_repository.dart';
import '../services/cloud_backup_service.dart';
import '../services/llm_service.dart';
import '../services/webdav_service.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final _formKey = GlobalKey<FormState>();
  late SettingsRepository _settingsRepository;
  
  // 控制器
  final _markdownFolderController = TextEditingController();
  final _watchFolderController = TextEditingController();
  final _llmBaseUrlController = TextEditingController();
  final _llmApiKeyController = TextEditingController();
  final _llmModelController = TextEditingController();
  final _systemPromptController = TextEditingController();
  final _backupApiUrlController = TextEditingController();
  final _backupApiKeyController = TextEditingController();
  final _webdavUrlController = TextEditingController();
  final _webdavUsernameController = TextEditingController();
  final _webdavPasswordController = TextEditingController();
  final _webdavRemotePathController = TextEditingController();

  // 状态变量
  bool _obscureApiKey = true;
  bool _obscureBackupKey = true;
  bool _obscureWebdavPassword = true;
  bool? _backupTestResult;
  bool? _llmTestResult;
  bool? _webdavTestResult;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeSettings();
  }

  Future<void> _initializeSettings() async {
    _settingsRepository = await SettingsRepository.create();
    await _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);
    try {
      final settings = await _settingsRepository.loadSettings();
      
      _markdownFolderController.text = settings.markdownFolder ?? _settingsRepository.getDefaultMarkdownFolder();
      _watchFolderController.text = settings.watchFolder ?? _settingsRepository.getDefaultWatchFolder();
      _llmBaseUrlController.text = settings.llmBaseUrl ?? '';
      _llmApiKeyController.text = settings.llmApiKey ?? '';
      _llmModelController.text = settings.llmModel ?? 'gpt-4o';
      _systemPromptController.text = settings.systemPrompt ?? '';
      _backupApiUrlController.text = settings.backupApiUrl ?? '';
      _backupApiKeyController.text = settings.backupApiKey ?? '';
      _webdavUrlController.text = settings.webdavUrl ?? '';
      _webdavUsernameController.text = settings.webdavUsername ?? '';
      _webdavPasswordController.text = settings.webdavPassword ?? '';
      _webdavRemotePathController.text = settings.webdavRemotePath ?? '/';
    } catch (e) {
      _showErrorSnackBar('加载设置失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildFolderSection(),
            const Divider(height: 32),
            _buildLLMSection(),
            const Divider(height: 32),
            _buildBackupSection(),
            const Divider(height: 32),
            _buildWebDAVSection(),
            const SizedBox(height: 32),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildFolderSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('文件夹设置', style: Theme.of(context).textTheme.headlineSmall),
        const SizedBox(height: 16),

        // Markdown保存文件夹
        Text('Markdown保存文件夹', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _markdownFolderController,
                decoration: const InputDecoration(
                  labelText: 'Markdown文件保存路径',
                  hintText: '/storage/emulated/0/HandwriteApp/markdown/',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入Markdown保存文件夹路径';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _selectMarkdownFolder,
              child: const Text('选择'),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 监控文件夹
        Text('监控文件夹', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _watchFolderController,
                decoration: const InputDecoration(
                  labelText: '图片监控文件夹路径',
                  hintText: '/storage/emulated/0/HandwriteApp/watch/',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入监控文件夹路径';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _selectWatchFolder,
              child: const Text('选择'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLLMSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text('LLM配置', style: Theme.of(context).textTheme.headlineSmall),
            const Spacer(),
            if (_llmTestResult != null)
              Icon(
                _llmTestResult! ? Icons.check_circle : Icons.error,
                color: _llmTestResult! ? Colors.green : Colors.red,
              ),
          ],
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _llmBaseUrlController,
          decoration: const InputDecoration(
            labelText: 'Base URL',
            hintText: 'https://api.openai.com/v1',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入LLM Base URL';
            }
            final uri = Uri.tryParse(value);
            if (uri == null || !uri.hasAbsolutePath) {
              return '请输入有效的URL';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _llmApiKeyController,
          decoration: InputDecoration(
            labelText: 'API Key',
            hintText: '输入您的API Key',
            border: const OutlineInputBorder(),
            suffixIcon: IconButton(
              icon: Icon(_obscureApiKey ? Icons.visibility : Icons.visibility_off),
              onPressed: () => setState(() => _obscureApiKey = !_obscureApiKey),
            ),
          ),
          obscureText: _obscureApiKey,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入API Key';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _llmModelController,
          decoration: const InputDecoration(
            labelText: '模型',
            hintText: 'gpt-4o',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入模型名称';
            }
            return null;
          },

        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _systemPromptController,
          decoration: const InputDecoration(
            labelText: '系统提示词',
            hintText: '输入自定义系统提示词',
            border: OutlineInputBorder(),
          ),
          maxLines: 5,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入系统提示词';
            }
            return null;
          },
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            ElevatedButton(
              onPressed: _resetToDefaultPrompt,
              child: const Text('恢复默认提示词'),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: _testLLMConnection,
              child: const Text('测试连接'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBackupSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text('云端备份', style: Theme.of(context).textTheme.headlineSmall),
            const Spacer(),
            if (_backupTestResult != null)
              Icon(
                _backupTestResult! ? Icons.check_circle : Icons.error,
                color: _backupTestResult! ? Colors.green : Colors.red,
              ),
          ],
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _backupApiUrlController,
          decoration: const InputDecoration(
            labelText: '备份API地址',
            hintText: 'https://your-backup-api.com/api',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              final uri = Uri.tryParse(value);
              if (uri == null || !uri.hasAbsolutePath) {
                return '请输入有效的URL';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _backupApiKeyController,
          decoration: InputDecoration(
            labelText: '备份API Key',
            hintText: '输入备份服务的API Key',
            border: const OutlineInputBorder(),
            suffixIcon: IconButton(
              icon: Icon(_obscureBackupKey ? Icons.visibility : Icons.visibility_off),
              onPressed: () => setState(() => _obscureBackupKey = !_obscureBackupKey),
            ),
          ),
          obscureText: _obscureBackupKey,
        ),
        const SizedBox(height: 16),
        ElevatedButton(
          onPressed: _testBackupConnection,
          child: const Text('测试连接'),
        ),
      ],
    );
  }

  Widget _buildWebDAVSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text('WebDAV配置', style: Theme.of(context).textTheme.headlineSmall),
            const Spacer(),
            if (_webdavTestResult != null)
              Icon(
                _webdavTestResult! ? Icons.check_circle : Icons.error,
                color: _webdavTestResult! ? Colors.green : Colors.red,
              ),
          ],
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _webdavUrlController,
          decoration: const InputDecoration(
            labelText: 'WebDAV服务器地址',
            hintText: 'https://your-webdav-server.com/webdav',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              final uri = Uri.tryParse(value);
              if (uri == null || !uri.hasAbsolutePath) {
                return '请输入有效的URL';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _webdavUsernameController,
          decoration: const InputDecoration(
            labelText: '用户名',
            hintText: '输入WebDAV用户名',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _webdavPasswordController,
          decoration: InputDecoration(
            labelText: '密码',
            hintText: '输入WebDAV密码',
            border: const OutlineInputBorder(),
            suffixIcon: IconButton(
              icon: Icon(_obscureWebdavPassword ? Icons.visibility : Icons.visibility_off),
              onPressed: () => setState(() => _obscureWebdavPassword = !_obscureWebdavPassword),
            ),
          ),
          obscureText: _obscureWebdavPassword,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _webdavRemotePathController,
          decoration: const InputDecoration(
            labelText: '远程路径',
            hintText: '/images/',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),
        ElevatedButton(
          onPressed: _testWebDAVConnection,
          child: const Text('测试连接'),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _saveSettings,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('保存设置'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: OutlinedButton(
            onPressed: _resetSettings,
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('重置设置'),
          ),
        ),
      ],
    );
  }

  Future<void> _selectMarkdownFolder() async {
    try {
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
      if (selectedDirectory != null) {
        setState(() {
          _markdownFolderController.text = selectedDirectory;
        });
      }
    } catch (e) {
      _showErrorSnackBar('选择Markdown文件夹失败: $e');
    }
  }

  Future<void> _selectWatchFolder() async {
    try {
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
      if (selectedDirectory != null) {
        setState(() {
          _watchFolderController.text = selectedDirectory;
        });

        // 如果选择的是content URI，提示用户
        if (selectedDirectory.startsWith('content://')) {
          _showWarningDialog(
            '路径格式提示',
            '检测到您选择的是系统URI格式路径：\n$selectedDirectory\n\n'
            '如果批量处理时提示"目录不存在"，请手动输入文件系统路径，例如：\n'
            '/storage/emulated/0/Download/images\n'
            '/storage/emulated/0/DCIM/Camera\n'
            '/storage/emulated/0/Pictures\n\n'
            '您也可以直接在上方文本框中修改路径。'
          );
        }
      }
    } catch (e) {
      _showErrorSnackBar('选择监控文件夹失败: $e');
    }
  }

  void _resetToDefaultPrompt() {
    const defaultPrompt = """你是一个专业的手写文字识别助手。请分析图片中的手写内容：

1. 准确识别所有手写文字内容
2. 保持原文的段落结构和逻辑顺序
3. 输出格式必须是标准Markdown格式
4. 如果有标题，请用适当的#标记层级
5. 如果有列表，请用-或数字标记
6. 忽略涂改、装饰性内容和无关标记
7. 如果内容不清晰，请用[不清晰]标注
8. 如果有表格，请用|和-来表示
9. 如果内容中有用五角星开头的内容，则将该内容作为markdown文件的标题，如果没有，你来根据内容自动生成一个标题

请直接输出转换结果，不要添加其他说明文字。""";

    setState(() {
      _systemPromptController.text = defaultPrompt;
    });
  }

  Future<void> _testLLMConnection() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _llmTestResult = null);

    try {
      final settings = AppSettings(
        llmBaseUrl: _llmBaseUrlController.text,
        llmApiKey: _llmApiKeyController.text,
        llmModel: _llmModelController.text,
      );

      final llmService = LLMService();
      final result = await llmService.testConnection(settings);

      setState(() => _llmTestResult = result);

      if (result) {
        _showSuccessSnackBar('LLM连接测试成功');
      } else {
        _showErrorSnackBar('LLM连接测试失败');
      }
    } catch (e) {
      setState(() => _llmTestResult = false);
      _showErrorSnackBar('LLM连接测试失败: $e');
    }
  }

  Future<void> _testBackupConnection() async {
    final apiUrl = _backupApiUrlController.text;
    final apiKey = _backupApiKeyController.text;

    if (apiUrl.isEmpty || apiKey.isEmpty) {
      _showErrorSnackBar('请先填写备份API地址和密钥');
      return;
    }

    setState(() => _backupTestResult = null);

    try {
      final backupService = CloudBackupService(
        apiUrl: apiUrl,
        apiKey: apiKey,
      );

      final result = await backupService.testConnection();

      setState(() => _backupTestResult = result);

      if (result) {
        _showSuccessSnackBar('备份连接测试成功');
      } else {
        _showErrorSnackBar('备份连接测试失败');
      }
    } catch (e) {
      setState(() => _backupTestResult = false);
      _showErrorSnackBar('备份连接测试失败: $e');
    }
  }

  Future<void> _testWebDAVConnection() async {
    final url = _webdavUrlController.text;
    final username = _webdavUsernameController.text;
    final password = _webdavPasswordController.text;

    if (url.isEmpty || username.isEmpty || password.isEmpty) {
      _showErrorSnackBar('请先填写WebDAV服务器地址、用户名和密码');
      return;
    }

    setState(() => _webdavTestResult = null);

    try {
      final settings = AppSettings(
        webdavUrl: url,
        webdavUsername: username,
        webdavPassword: password,
        webdavRemotePath: _webdavRemotePathController.text.isEmpty ? '/' : _webdavRemotePathController.text,
      );

      final webdavService = WebDAVService();
      final result = await webdavService.testConnection(settings);

      setState(() => _webdavTestResult = result);

      if (result) {
        _showSuccessSnackBar('WebDAV连接测试成功');
      } else {
        _showErrorSnackBar('WebDAV连接测试失败');
      }
    } catch (e) {
      setState(() => _webdavTestResult = false);
      _showErrorSnackBar('WebDAV连接测试失败: $e');
    }
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      // 保存文件夹设置
      await _settingsRepository.saveFolderSettings(
        markdownFolder: _markdownFolderController.text,
        watchFolder: _watchFolderController.text,
      );

      // 保存LLM设置
      await _settingsRepository.saveLLMSettings(
        baseUrl: _llmBaseUrlController.text,
        apiKey: _llmApiKeyController.text,
        model: _llmModelController.text,
        systemPrompt: _systemPromptController.text,
      );

      // 保存备份设置（如果填写了）
      if (_backupApiUrlController.text.isNotEmpty && _backupApiKeyController.text.isNotEmpty) {
        await _settingsRepository.saveBackupSettings(
          apiUrl: _backupApiUrlController.text,
          apiKey: _backupApiKeyController.text,
        );
      }

      // 保存WebDAV设置（如果填写了）
      if (_webdavUrlController.text.isNotEmpty &&
          _webdavUsernameController.text.isNotEmpty &&
          _webdavPasswordController.text.isNotEmpty) {
        await _settingsRepository.saveWebDAVSettings(
          url: _webdavUrlController.text,
          username: _webdavUsernameController.text,
          password: _webdavPasswordController.text,
          remotePath: _webdavRemotePathController.text.isEmpty ? '/' : _webdavRemotePathController.text,
        );
      }

      _showSuccessSnackBar('设置保存成功');
      if (mounted) {
        Navigator.of(context).pop(true); // 返回true表示设置已更新
      }
    } catch (e) {
      _showErrorSnackBar('保存设置失败: $e');
    }
  }

  Future<void> _resetSettings() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置设置'),
        content: const Text('确定要重置所有设置吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _settingsRepository.clearAllSettings();
        await _loadSettings();
        _showSuccessSnackBar('设置已重置');
      } catch (e) {
        _showErrorSnackBar('重置设置失败: $e');
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showWarningDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SingleChildScrollView(
          child: Text(message),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _markdownFolderController.dispose();
    _watchFolderController.dispose();
    _llmBaseUrlController.dispose();
    _llmApiKeyController.dispose();
    _llmModelController.dispose();
    _systemPromptController.dispose();
    _backupApiUrlController.dispose();
    _backupApiKeyController.dispose();
    super.dispose();
  }
}
