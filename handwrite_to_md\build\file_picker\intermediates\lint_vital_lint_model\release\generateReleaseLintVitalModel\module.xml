<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android"
    name=":file_picker"
    type="LIBRARY"
    maven="com.mr.flutter.plugin.filepicker:file_picker:1.0-SNAPSHOT"
    agpVersion="8.7.0"
    buildFolder="D:\android\handwritetomd\handwrite_to_md\build\file_picker"
    bootClassPath="D:\Tools\android_sdk\platforms\android-34\android.jar;D:\Tools\android_sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      disable="InvalidPackage"
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="InvalidPackage"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
