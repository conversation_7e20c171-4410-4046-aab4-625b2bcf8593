{"buildFiles": ["D:\\Tools\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Tools\\android_sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\android\\handwritetomd\\handwrite_to_md\\android\\app\\.cxx\\RelWithDebInfo\\1a3w2w6n\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\Tools\\android_sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\android\\handwritetomd\\handwrite_to_md\\android\\app\\.cxx\\RelWithDebInfo\\1a3w2w6n\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}