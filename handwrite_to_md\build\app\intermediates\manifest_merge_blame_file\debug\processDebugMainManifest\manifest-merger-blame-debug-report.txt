1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.handwrite_to_md"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:21:5-67
15-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:21:22-64
16    <!-- 相机权限 -->
17    <uses-permission android:name="android.permission.CAMERA" /> <!-- 存储权限 (兼容所有Android版本) -->
17-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:4:5-65
17-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:4:22-62
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:7:5-80
18-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:7:22-77
19    <uses-permission
19-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:8:5-10:40
20        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
20-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:8:22-78
21        android:maxSdkVersion="28" /> <!-- Android 13+ 媒体权限 -->
21-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:9:9-35
22    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
22-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:13:5-76
22-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:13:22-73
23    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
23-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:14:5-75
23-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:14:22-72
24    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" /> <!-- Android 11+ 管理外部存储权限 (最全面的文件访问权限) -->
24-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:15:5-75
24-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:15:22-72
25    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
25-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:18:5-82
25-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:18:22-79
26    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 请求忽略电池优化，避免后台权限问题 -->
26-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:22:5-79
26-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:22:22-76
27    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
27-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:25:5-95
27-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:25:22-92
28    <!--
29 Required to query activities that can process text, see:
30         https://developer.android.com/training/package-visibility and
31         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
32
33         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
34    -->
35    <queries>
35-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:64:5-69:15
36        <intent>
36-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:65:9-68:18
37            <action android:name="android.intent.action.PROCESS_TEXT" />
37-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:66:13-72
37-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:66:21-70
38
39            <data android:mimeType="text/plain" />
39-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:67:13-50
39-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:67:19-48
40        </intent>
41        <intent>
41-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
42            <action android:name="android.intent.action.GET_CONTENT" />
42-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
42-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
43
44            <data android:mimeType="*/*" />
44-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:67:13-50
44-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:67:19-48
45        </intent>
46    </queries>
47
48    <permission
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
49        android:name="com.example.handwrite_to_md.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
50        android:protectionLevel="signature" />
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
51
52    <uses-permission android:name="com.example.handwrite_to_md.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
53
54    <application
55        android:name="android.app.Application"
56        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
56-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
57        android:debuggable="true"
58        android:extractNativeLibs="true"
59        android:icon="@mipmap/ic_launcher"
60        android:label="ToMDs" >
61        <activity
62            android:name="com.example.handwrite_to_md.MainActivity"
63            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
64            android:exported="true"
65            android:hardwareAccelerated="true"
66            android:launchMode="singleTop"
67            android:taskAffinity=""
68            android:theme="@style/LaunchTheme"
69            android:windowSoftInputMode="adjustResize" >
70
71            <!--
72                 Specifies an Android theme to apply to this Activity as soon as
73                 the Android process has started. This theme is visible to the user
74                 while the Flutter UI initializes. After that, this theme continues
75                 to determine the Window background behind the Flutter UI.
76            -->
77            <meta-data
78                android:name="io.flutter.embedding.android.NormalTheme"
79                android:resource="@style/NormalTheme" />
80
81            <intent-filter>
82                <action android:name="android.intent.action.MAIN" />
83
84                <category android:name="android.intent.category.LAUNCHER" />
85            </intent-filter>
86        </activity>
87        <!--
88             Don't delete the meta-data below.
89             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
90        -->
91        <meta-data
92            android:name="flutterEmbedding"
93            android:value="2" />
94
95        <provider
95-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
96            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
96-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
97            android:authorities="com.example.handwrite_to_md.flutter.image_provider"
97-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
98            android:exported="false"
98-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
99            android:grantUriPermissions="true" >
99-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
100            <meta-data
100-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
101                android:name="android.support.FILE_PROVIDER_PATHS"
101-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
102                android:resource="@xml/flutter_image_picker_file_paths" />
102-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
103        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
104        <service
104-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
105            android:name="com.google.android.gms.metadata.ModuleDependencies"
105-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
106            android:enabled="false"
106-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
107            android:exported="false" >
107-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
108            <intent-filter>
108-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
109                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
109-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
109-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
110            </intent-filter>
111
112            <meta-data
112-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
113                android:name="photopicker_activity:0:required"
113-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
114                android:value="" />
114-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
115        </service>
116
117        <uses-library
117-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
118            android:name="androidx.window.extensions"
118-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
119            android:required="false" />
119-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
120        <uses-library
120-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
121            android:name="androidx.window.sidecar"
121-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
122            android:required="false" />
122-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
123
124        <provider
124-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
125            android:name="androidx.startup.InitializationProvider"
125-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
126            android:authorities="com.example.handwrite_to_md.androidx-startup"
126-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
127            android:exported="false" >
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
128            <meta-data
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
129                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
130                android:value="androidx.startup" />
130-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
131            <meta-data
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
132                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
133                android:value="androidx.startup" />
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
134        </provider>
135
136        <receiver
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
137            android:name="androidx.profileinstaller.ProfileInstallReceiver"
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
138            android:directBootAware="false"
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
139            android:enabled="true"
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
140            android:exported="true"
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
141            android:permission="android.permission.DUMP" >
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
142            <intent-filter>
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
143                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
144            </intent-filter>
145            <intent-filter>
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
146                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
147            </intent-filter>
148            <intent-filter>
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
149                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
150            </intent-filter>
151            <intent-filter>
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
152                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
153            </intent-filter>
154        </receiver>
155    </application>
156
157</manifest>
