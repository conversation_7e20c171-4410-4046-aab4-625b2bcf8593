{"logs": [{"outputFile": "io.flutter.plugins.sharedpreferences.shared_preferences_android-mergeReleaseResources-38:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a4dcd3e8c92efa77cfa193b658940d9\\transformed\\appcompat-1.1.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,897,988,1080,1172,1266,1367,1460,1562,1657,1748,1839,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,75,90,91,91,93,100,92,101,94,90,90,77,106,103,95,106,102,108,155,97,78", "endOffsets": "214,313,425,510,616,736,816,892,983,1075,1167,1261,1362,1455,1557,1652,1743,1834,1912,2019,2123,2219,2326,2429,2538,2694,2792,2871"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,897,988,1080,1172,1266,1367,1460,1562,1657,1748,1839,1917,2024,2128,2224,2331,2434,2543,2699,3878", "endColumns": "113,98,111,84,105,119,79,75,90,91,91,93,100,92,101,94,90,90,77,106,103,95,106,102,108,155,97,78", "endOffsets": "214,313,425,510,616,736,816,892,983,1075,1167,1261,1362,1455,1557,1652,1743,1834,1912,2019,2123,2219,2326,2429,2538,2694,2792,3952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,341,473,642,725", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "171,258,336,468,637,720,798"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3510,3581,3668,3746,4058,4227,4310", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "3576,3663,3741,3873,4222,4305,4383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2797,2894,2996,3094,3191,3293,3399,3957", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "2889,2991,3089,3186,3288,3394,3505,4053"}}]}, {"outputFile": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a4dcd3e8c92efa77cfa193b658940d9\\transformed\\appcompat-1.1.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,897,988,1080,1172,1266,1367,1460,1562,1657,1748,1839,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,75,90,91,91,93,100,92,101,94,90,90,77,106,103,95,106,102,108,155,97,78", "endOffsets": "214,313,425,510,616,736,816,892,983,1075,1167,1261,1362,1455,1557,1652,1743,1834,1912,2019,2123,2219,2326,2429,2538,2694,2792,2871"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,897,988,1080,1172,1266,1367,1460,1562,1657,1748,1839,1917,2024,2128,2224,2331,2434,2543,2699,3878", "endColumns": "113,98,111,84,105,119,79,75,90,91,91,93,100,92,101,94,90,90,77,106,103,95,106,102,108,155,97,78", "endOffsets": "214,313,425,510,616,736,816,892,983,1075,1167,1261,1362,1455,1557,1652,1743,1834,1912,2019,2123,2219,2326,2429,2538,2694,2792,3952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,341,473,642,725", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "171,258,336,468,637,720,798"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3510,3581,3668,3746,4058,4227,4310", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "3576,3663,3741,3873,4222,4305,4383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2797,2894,2996,3094,3191,3293,3399,3957", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "2889,2991,3089,3186,3288,3394,3505,4053"}}]}]}