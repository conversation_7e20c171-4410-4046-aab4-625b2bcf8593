<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2018 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
        android:name="btn_checkbox_unchecked"
        android:width="32dp"
        android:viewportWidth="48"
        android:height="32dp"
        android:viewportHeight="48">
    <group
            android:name="icon_null"
            android:translateX="24"
            android:translateY="24"
            android:scaleX="0.2"
            android:scaleY="0.2">
        <group
                android:name="check"
                android:scaleX="7.5"
                android:scaleY="7.5">
            <path
                    android:name="box_outer_merged"
                    android:pathData="M 7.0,-9.0 c 0.0,0.0 -14.0,0.0 -14.0,0.0 c -1.**********,0.0 -2.0,0.********** -2.0,2.0 c 0.0,0.0 0.0,14.0 0.0,14.0 c 0.0,1.********** 0.**********,2.0 2.0,2.0 c 0.0,0.0 14.0,0.0 14.0,0.0 c 1.**********,0.0 2.0,-0.********** 2.0,-2.0 c 0.0,0.0 0.0,-14.0 0.0,-14.0 c 0.0,-1.********** -0.**********,-2.0 -2.0,-2.0 c 0.0,0.0 0.0,0.0 0.0,0.0 Z M -2.0,5.00001525879 c 0.0,0.0 -1.4234161377,-1.40159606934 -1.4234161377,-1.40159606934 c 0.0,0.0 1.41409301758,-1.41409301758 1.41409301758,-1.41409301758 c 0.0,0.0 0.00932312011719,-0.0124053955078 0.00932312011719,-0.0124053955078 c 0.0,0.0 0.0234069824219,-0.0235137939453 0.0234069824219,-0.0235137939453 c 0.0,0.0 1.41409301758,1.41409301758 1.41409301758,1.41409301758 c 0.0,0.0 -1.4375,1.43751525879 -1.4375,1.43751525879 Z"
                    android:fillColor="#FF000000"
                    android:fillAlpha="0"/>
        </group>
        <group
                android:name="box_dilate"
                android:scaleX="7.5"
                android:scaleY="7.5">
            <path
                    android:name="box_inner_merged"
                    android:pathData="M -7.0,-7.0 l 14.0,0.0 c 0.0,0.0 0.0,0.0 0.0,0.0 l 0.0,14.0 c 0.0,0.0 0.0,0.0 0.0,0.0 l -14.0,0.0 c 0.0,0.0 0.0,0.0 0.0,0.0 l 0.0,-14.0 c 0.0,0.0 0.0,0.0 0.0,0.0 Z M 7.0,-9.0 c 0.0,0.0 -14.0,0.0 -14.0,0.0 c -1.**********,0.0 -2.0,0.********** -2.0,2.0 c 0.0,0.0 0.0,14.0 0.0,14.0 c 0.0,1.********** 0.**********,2.0 2.0,2.0 c 0.0,0.0 14.0,0.0 14.0,0.0 c 1.**********,0.0 2.0,-0.********** 2.0,-2.0 c 0.0,0.0 0.0,-14.0 0.0,-14.0 c 0.0,-1.********** -0.**********,-2.0 -2.0,-2.0 c 0.0,0.0 0.0,0.0 0.0,0.0 Z"
                    android:fillColor="#FF000000"/>
        </group>
    </group>
</vector>
